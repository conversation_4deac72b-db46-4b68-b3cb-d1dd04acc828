const http = require('http');
const fs = require('fs');
const path = require('path');

const port = 3000;

// MIME types
const mimeTypes = {
  '.html': 'text/html',
  '.js': 'text/javascript',
  '.css': 'text/css',
  '.json': 'application/json',
  '.png': 'image/png',
  '.jpg': 'image/jpg',
  '.gif': 'image/gif',
  '.svg': 'image/svg+xml',
  '.wav': 'audio/wav',
  '.mp4': 'video/mp4',
  '.woff': 'application/font-woff',
  '.ttf': 'application/font-ttf',
  '.eot': 'application/vnd.ms-fontobject',
  '.otf': 'application/font-otf',
  '.wasm': 'application/wasm'
};

// Mock API data
const mockData = {
  patients: [
    {
      id: '1',
      patientId: 'P001',
      firstName: '<PERSON>',
      lastName: 'Doe',
      email: '<EMAIL>',
      phone: '(*************',
      dateOfBirth: '1985-06-15',
      gender: 'Male',
      address: '123 Main St, City, State 12345',
      createdAt: '2024-01-15T10:30:00Z',
      updatedAt: '2024-01-15T10:30:00Z'
    },
    {
      id: '2',
      patientId: 'P002',
      firstName: 'Jane',
      lastName: 'Smith',
      email: '<EMAIL>',
      phone: '(*************',
      dateOfBirth: '1990-03-22',
      gender: 'Female',
      address: '456 Oak Ave, City, State 12345',
      createdAt: '2024-01-16T14:20:00Z',
      updatedAt: '2024-01-16T14:20:00Z'
    }
  ],
  appointments: [
    {
      id: '1',
      patientId: '1',
      providerId: 'dr1',
      date: '2024-12-14T14:00:00Z',
      endTime: '2024-12-14T14:30:00Z',
      duration: 30,
      type: 'CONSULTATION',
      status: 'CONFIRMED',
      title: 'Regular Checkup',
      patient: { firstName: 'John', lastName: 'Doe', patientId: 'P001' },
      provider: { firstName: 'Dr. Sarah', lastName: 'Johnson' }
    }
  ],
  labResults: [
    {
      id: '1',
      patientId: '1',
      testType: 'COMPLETE_BLOOD_COUNT',
      testDate: '2024-12-10T09:00:00Z',
      status: 'COMPLETED',
      results: {
        'White Blood Cells': 7.2,
        'Red Blood Cells': 4.8,
        'Hemoglobin': 14.5,
        'Hematocrit': 42.1
      },
      normalRanges: {
        'White Blood Cells': { min: 4.0, max: 11.0, unit: 'K/uL' },
        'Red Blood Cells': { min: 4.2, max: 5.4, unit: 'M/uL' },
        'Hemoglobin': { min: 12.0, max: 16.0, unit: 'g/dL' },
        'Hematocrit': { min: 36.0, max: 46.0, unit: '%' }
      },
      orderedBy: 'Dr. Sarah Johnson',
      patient: { firstName: 'John', lastName: 'Doe', patientId: 'P001' }
    }
  ]
};

const server = http.createServer((req, res) => {
  // Enable CORS
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  if (req.method === 'OPTIONS') {
    res.writeHead(200);
    res.end();
    return;
  }

  const url = req.url;
  
  // API Routes
  if (url.startsWith('/api/')) {
    res.setHeader('Content-Type', 'application/json');
    
    if (url === '/api/patients') {
      res.writeHead(200);
      res.end(JSON.stringify({
        success: true,
        data: mockData.patients,
        pagination: {
          page: 1,
          limit: 10,
          total: mockData.patients.length,
          totalPages: 1,
          hasNext: false,
          hasPrev: false
        }
      }));
      return;
    }
    
    if (url === '/api/appointments') {
      res.writeHead(200);
      res.end(JSON.stringify({
        success: true,
        data: mockData.appointments,
        pagination: {
          page: 1,
          limit: 10,
          total: mockData.appointments.length,
          totalPages: 1,
          hasNext: false,
          hasPrev: false
        }
      }));
      return;
    }
    
    if (url === '/api/lab-results') {
      res.writeHead(200);
      res.end(JSON.stringify({
        success: true,
        data: mockData.labResults,
        pagination: {
          page: 1,
          limit: 10,
          total: mockData.labResults.length,
          totalPages: 1,
          hasNext: false,
          hasPrev: false
        }
      }));
      return;
    }
    
    if (url === '/api/analytics/dashboard') {
      res.writeHead(200);
      res.end(JSON.stringify({
        success: true,
        data: {
          analytics: {
            patients: {
              total: 1247,
              newThisWeek: 23,
              newThisMonth: 89,
              growthRate: 7.2
            },
            appointments: {
              total: 342,
              today: 18,
              thisWeek: 127,
              upcoming: 89
            },
            labResults: {
              total: 856,
              pending: 12,
              flagged: 34,
              flaggedPercentage: 4.0
            }
          }
        }
      }));
      return;
    }
    
    // Default API response
    res.writeHead(404);
    res.end(JSON.stringify({ success: false, error: 'API endpoint not found' }));
    return;
  }

  // Serve static files
  let filePath = '.' + url;
  if (filePath === './') {
    filePath = './enhanced-demo.html';
  }

  const extname = String(path.extname(filePath)).toLowerCase();
  const mimeType = mimeTypes[extname] || 'application/octet-stream';

  fs.readFile(filePath, (error, content) => {
    if (error) {
      if (error.code === 'ENOENT') {
        res.writeHead(404);
        res.end('File not found');
      } else {
        res.writeHead(500);
        res.end('Server error: ' + error.code);
      }
    } else {
      res.writeHead(200, { 'Content-Type': mimeType });
      res.end(content, 'utf-8');
    }
  });
});

server.listen(port, () => {
  console.log(`🏥 Healthcare Management System running at http://localhost:${port}`);
  console.log('📊 Mock API endpoints available:');
  console.log('  - GET /api/patients');
  console.log('  - GET /api/appointments');
  console.log('  - GET /api/lab-results');
  console.log('  - GET /api/analytics/dashboard');
});
