export interface Appointment {
  id: string;
  patientId: string;
  date: string;
  duration: number;
  type: 'INITIAL_CONSULTATION' | 'FOLLOW_UP' | 'THERAPY_SESSION' | 'MEDICATION_REVIEW' | 'EMERGENCY';
  status: 'SCHEDULED' | 'CONFIRMED' | 'IN_PROGRESS' | 'COMPLETED' | 'CANCELLED' | 'NO_SHOW';
  notes?: string;
  createdAt: string;
  updatedAt: string;
  // Relations
  patient?: {
    id: string;
    firstName: string;
    lastName: string;
    patientId: string;
  };
}

export interface AppointmentFormData {
  patientId: string;
  date: string;
  time: string;
  duration: string;
  type: string;
  status: string;
  notes: string;
}

export interface AppointmentFilters {
  patientId?: string;
  status?: string;
  type?: string;
  dateFrom?: string;
  dateTo?: string;
}

export interface AppointmentStats {
  total: number;
  scheduled: number;
  completed: number;
  cancelled: number;
  todayCount: number;
}
