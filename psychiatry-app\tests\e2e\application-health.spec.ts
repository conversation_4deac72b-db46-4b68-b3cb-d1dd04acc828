import { test, expect } from '@playwright/test';

test.describe('Application Health', () => {
  test('should load the application successfully', async ({ page }) => {
    console.log('🔄 Testing application health...');
    
    // Navigate to the application
    await page.goto('/');
    
    // Wait for the page to load
    await page.waitForLoadState('networkidle');
    
    // Check if the page title is correct
    await expect(page).toHaveTitle(/Psychiatry Patient Management System/);
    console.log('✅ Page title is correct');
    
    // Check if the body is visible
    await expect(page.locator('body')).toBeVisible();
    console.log('✅ Page body is visible');
    
    // Check if React root is present
    await expect(page.locator('#root')).toBeVisible();
    console.log('✅ React root element is present');
    
    // Check if the app content is loaded (should show login page or dashboard)
    const hasLoginForm = await page.locator('form').count() > 0;
    const hasDashboard = await page.locator('nav, .navbar, .sidebar').count() > 0;
    
    expect(hasLoginForm || hasDashboard).toBe(true);
    
    if (hasLoginForm) {
      console.log('✅ Login form is displayed');
      
      // Check for email and password fields
      await expect(page.locator('input[type="email"], input[name="email"]')).toBeVisible();
      await expect(page.locator('input[type="password"], input[name="password"]')).toBeVisible();
      console.log('✅ Login form fields are present');
    }
    
    if (hasDashboard) {
      console.log('✅ Dashboard is displayed');
    }
    
    console.log('🎉 Application health check passed!');
  });

  test('should handle backend connectivity', async ({ page }) => {
    console.log('🔄 Testing backend connectivity...');
    
    // Track API responses
    const apiResponses: string[] = [];
    
    page.on('response', (response) => {
      if (response.url().includes('/api/')) {
        apiResponses.push(`${response.status()} - ${response.url()}`);
      }
    });
    
    // Navigate to the application
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    
    // Wait a bit for any API calls to complete
    await page.waitForTimeout(2000);
    
    console.log(`📡 API calls made: ${apiResponses.length}`);
    apiResponses.forEach(response => console.log(`   ${response}`));
    
    // The test passes if the page loads, regardless of API calls
    // This is because the app might not make API calls on initial load
    await expect(page.locator('body')).toBeVisible();
    console.log('✅ Backend connectivity test completed');
  });

  test('should not have console errors', async ({ page }) => {
    console.log('🔄 Checking for console errors...');
    
    const consoleErrors: string[] = [];
    
    page.on('console', (msg) => {
      if (msg.type() === 'error') {
        consoleErrors.push(msg.text());
      }
    });
    
    page.on('pageerror', (error) => {
      consoleErrors.push(`Page Error: ${error.message}`);
    });
    
    // Navigate to the application
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    
    // Wait a bit for any delayed errors
    await page.waitForTimeout(3000);
    
    console.log(`❌ Console errors found: ${consoleErrors.length}`);
    consoleErrors.forEach(error => console.log(`   ${error}`));
    
    // For now, we'll log errors but not fail the test
    // In a production environment, you might want to fail on certain errors
    if (consoleErrors.length > 0) {
      console.log('⚠️ Console errors detected but test will continue');
    } else {
      console.log('✅ No console errors detected');
    }
    
    // Test passes if the page loads
    await expect(page.locator('body')).toBeVisible();
  });

  test('should be responsive', async ({ page }) => {
    console.log('🔄 Testing responsive design...');
    
    // Test different viewport sizes
    const viewports = [
      { name: 'Mobile', width: 375, height: 667 },
      { name: 'Tablet', width: 768, height: 1024 },
      { name: 'Desktop', width: 1280, height: 720 }
    ];
    
    for (const viewport of viewports) {
      console.log(`📱 Testing ${viewport.name} viewport (${viewport.width}x${viewport.height})`);
      
      await page.setViewportSize({ width: viewport.width, height: viewport.height });
      await page.goto('/');
      await page.waitForLoadState('networkidle');
      
      // Check if content is still visible and accessible
      await expect(page.locator('body')).toBeVisible();
      await expect(page.locator('#root')).toBeVisible();
      
      // Check if content fits in viewport (no horizontal scroll)
      const bodyWidth = await page.locator('body').evaluate(el => el.scrollWidth);
      expect(bodyWidth).toBeLessThanOrEqual(viewport.width + 20); // Allow small margin
      
      console.log(`✅ ${viewport.name} viewport test passed`);
    }
    
    // Reset to desktop
    await page.setViewportSize({ width: 1280, height: 720 });
    console.log('🎉 Responsive design test completed!');
  });
});
