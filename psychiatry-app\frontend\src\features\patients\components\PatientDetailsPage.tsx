import React from 'react';
import { useParams, useNavigate, <PERSON> } from 'react-router-dom';
import { <PERSON>, <PERSON>H<PERSON><PERSON>, CardTitle, CardContent, Button, Badge } from '../../../components/ui';
import { ArrowLeft, Edit, Brain, Calendar, FileText, Phone, Mail, MapPin, User, Heart } from 'lucide-react';
import { usePatient } from '../hooks/usePatient';
import LoadingSpinner from '../../../components/ui/LoadingSpinner';

const PatientDetailsPage: React.FC = () => {
  const { patientId } = useParams<{ patientId: string }>();
  const navigate = useNavigate();
  const { patient, loading, error } = usePatient(patientId!);

  if (loading) {
    return <LoadingSpinner />;
  }

  if (error || !patient) {
    return (
      <div className="text-center py-8">
        <p className="text-red-600 mb-4">
          {error || 'Patient not found'}
        </p>
        <Button onClick={() => navigate('/patients')}>
          Back to Patients
        </Button>
      </div>
    );
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const calculateAge = (dateOfBirth: string) => {
    const today = new Date();
    const birthDate = new Date(dateOfBirth);
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }
    
    return age;
  };

  return (
    <div className="max-w-6xl mx-auto space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="ghost"
            onClick={() => navigate('/patients')}
            leftIcon={<ArrowLeft className="h-4 w-4" />}
          >
            Back to Patients
          </Button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">
              {patient.firstName} {patient.lastName}
            </h1>
            <p className="text-gray-600">Patient ID: {patient.patientId}</p>
          </div>
        </div>
        <div className="flex space-x-3">
          <Button
            variant="outline"
            leftIcon={<Edit className="h-4 w-4" />}
            onClick={() => navigate(`/patients/${patient.id}/edit`)}
          >
            Edit Patient
          </Button>
          <Link to={`/history-taking/${patient.id}`}>
            <Button
              leftIcon={<Brain className="h-4 w-4" />}
            >
              Start Assessment
            </Button>
          </Link>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Basic Information */}
        <div className="lg:col-span-2 space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <User className="h-5 w-5" />
                <span>Basic Information</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-500">Full Name</label>
                  <p className="text-gray-900">{patient.firstName} {patient.lastName}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Date of Birth</label>
                  <p className="text-gray-900">
                    {formatDate(patient.dateOfBirth)} (Age {calculateAge(patient.dateOfBirth)})
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Gender</label>
                  <p className="text-gray-900">{patient.gender}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Status</label>
                  <Badge variant={patient.isActive ? "success" : "secondary"}>
                    {patient.isActive ? 'Active' : 'Inactive'}
                  </Badge>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Contact Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Phone className="h-5 w-5" />
                <span>Contact Information</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {patient.phone && (
                  <div className="flex items-center space-x-2">
                    <Phone className="h-4 w-4 text-gray-400" />
                    <span>{patient.phone}</span>
                  </div>
                )}
                {patient.email && (
                  <div className="flex items-center space-x-2">
                    <Mail className="h-4 w-4 text-gray-400" />
                    <span>{patient.email}</span>
                  </div>
                )}
                {patient.address && (
                  <div className="flex items-start space-x-2 md:col-span-2">
                    <MapPin className="h-4 w-4 text-gray-400 mt-0.5" />
                    <span>
                      {patient.address.street}, {patient.address.city}, {patient.address.state} {patient.address.zipCode}, {patient.address.country}
                    </span>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Medical Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Heart className="h-5 w-5" />
                <span>Medical Information</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {patient.medicalHistory && (
                <div>
                  <label className="text-sm font-medium text-gray-500">Medical History</label>
                  <p className="text-gray-900 mt-1">{patient.medicalHistory}</p>
                </div>
              )}
              {patient.allergies && (
                <div>
                  <label className="text-sm font-medium text-gray-500">Allergies</label>
                  <p className="text-gray-900 mt-1">{patient.allergies}</p>
                </div>
              )}
              {patient.currentMeds && (
                <div>
                  <label className="text-sm font-medium text-gray-500">Current Medications</label>
                  <p className="text-gray-900 mt-1">{patient.currentMeds}</p>
                </div>
              )}
              {patient.notes && (
                <div>
                  <label className="text-sm font-medium text-gray-500">Notes</label>
                  <p className="text-gray-900 mt-1">{patient.notes}</p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <Link to={`/history-taking/${patient.id}`} className="block">
                <Button className="w-full" leftIcon={<Brain className="h-4 w-4" />}>
                  Start Assessment
                </Button>
              </Link>
              <Link to={`/appointments/new?patientId=${patient.id}`} className="block">
                <Button variant="outline" className="w-full" leftIcon={<Calendar className="h-4 w-4" />}>
                  Schedule Appointment
                </Button>
              </Link>
              <Link to={`/lab-results/new?patientId=${patient.id}`} className="block">
                <Button variant="outline" className="w-full" leftIcon={<FileText className="h-4 w-4" />}>
                  Add Lab Result
                </Button>
              </Link>
            </CardContent>
          </Card>

          {/* Patient Summary */}
          <Card>
            <CardHeader>
              <CardTitle>Patient Summary</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-500">Created:</span>
                  <span>{formatDate(patient.createdAt)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-500">Last Updated:</span>
                  <span>{formatDate(patient.updatedAt)}</span>
                </div>

              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default PatientDetailsPage;
