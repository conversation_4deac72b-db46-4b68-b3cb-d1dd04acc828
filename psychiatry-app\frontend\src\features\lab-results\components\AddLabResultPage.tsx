import React from 'react';
import { Link } from 'react-router-dom';
import { Heart } from 'lucide-react';

const AddLabResultPage: React.FC = () => {
  return (
    <div className="max-w-2xl mx-auto space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-gray-900">Add Lab Result</h1>
        <Link
          to="/lab-results"
          className="text-gray-600 hover:text-gray-800"
        >
          ← Back to Lab Results
        </Link>
      </div>

      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8 text-center">
        <Heart className="h-16 w-16 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">Lab Result Entry</h3>
        <p className="text-gray-600 mb-4">
          This feature is being migrated to the new architecture.
        </p>
        <p className="text-sm text-gray-500">
          Coming soon with enhanced lab result entry capabilities.
        </p>
      </div>
    </div>
  );
};

export default AddLabResultPage;
