@echo off
echo ========================================
echo   Psychiatry Patient Management System
echo   Starting with PM2 Process Manager
echo ========================================
echo.

REM Change to the project directory
cd /d "%~dp0"

REM Check if we're in the right directory
if not exist "package.json" (
    echo ERROR: Not in the correct project directory
    echo Current directory: %CD%
    pause
    exit /b 1
)

REM Create logs directory if it doesn't exist
if not exist "logs" mkdir logs

REM Check if PM2 is installed globally
pm2 --version >nul 2>&1
if errorlevel 1 (
    echo PM2 is not installed globally. Installing PM2...
    call npm install -g pm2
    if errorlevel 1 (
        echo ERROR: Failed to install PM2
        echo Please run: npm install -g pm2
        pause
        exit /b 1
    )
)

REM Install dependencies if needed
if not exist "backend\node_modules" (
    echo Installing backend dependencies...
    cd backend
    call npm install
    if errorlevel 1 (
        echo ERROR: Failed to install backend dependencies
        pause
        exit /b 1
    )
    cd ..
)

if not exist "frontend\node_modules" (
    echo Installing frontend dependencies...
    cd frontend
    call npm install
    if errorlevel 1 (
        echo ERROR: Failed to install frontend dependencies
        pause
        exit /b 1
    )
    cd ..
)

REM Install root dependencies for PM2 ecosystem
if exist "package.json" (
    if not exist "node_modules" (
        echo Installing root dependencies...
        call npm install
        if errorlevel 1 (
            echo ERROR: Failed to install root dependencies
            pause
            exit /b 1
        )
    )
)

REM Generate Prisma client if needed
echo Generating database client...
cd backend
call npx prisma generate
if errorlevel 1 (
    echo ERROR: Failed to generate Prisma client
    cd ..
    pause
    exit /b 1
)

REM Setup database
echo Setting up database...
call npx prisma db push --accept-data-loss
if errorlevel 1 (
    echo ERROR: Failed to setup database
    cd ..
    pause
    exit /b 1
)

cd ..

REM Stop any existing PM2 processes
echo Stopping any existing processes...
call pm2 delete all >nul 2>&1

REM Start the application with PM2
echo.
echo ========================================
echo   Starting Application with PM2
echo ========================================
echo.

call pm2 start ecosystem.config.js
if errorlevel 1 (
    echo ERROR: Failed to start application with PM2
    pause
    exit /b 1
)

REM Display running processes
echo.
echo Current PM2 processes:
call pm2 list

echo.
echo ========================================
echo   Application Started Successfully!
echo ========================================
echo.
echo Backend: http://localhost:3002
echo Frontend: http://localhost:5173
echo.
echo PM2 Commands:
echo   pm2 list          - Show running processes
echo   pm2 logs          - Show logs for all processes
echo   pm2 logs backend  - Show backend logs only
echo   pm2 logs frontend - Show frontend logs only
echo   pm2 restart all   - Restart all processes
echo   pm2 stop all      - Stop all processes
echo   pm2 delete all    - Delete all processes
echo.
echo Press any key to show live logs (Ctrl+C to exit logs)...
pause >nul

REM Show live logs
call pm2 logs
