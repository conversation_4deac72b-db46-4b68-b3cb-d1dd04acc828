import { test, expect } from '@playwright/test';

test.describe('Application Navigation', () => {
  test('should navigate to different pages', async ({ page }) => {
    console.log('🔄 Testing application navigation...');
    
    // Test different routes
    const routes = [
      { path: '/', name: 'Home/Dashboard' },
      { path: '/login', name: 'Login' },
      { path: '/patients', name: 'Patients' },
      { path: '/appointments', name: 'Appointments' },
      { path: '/lab-results', name: 'Lab Results' },
      { path: '/users', name: 'Users' },
      { path: '/analytics', name: 'Analytics' }
    ];
    
    for (const route of routes) {
      console.log(`📍 Testing route: ${route.path} (${route.name})`);
      
      try {
        await page.goto(route.path);
        await page.waitForLoadState('networkidle', { timeout: 10000 });
        
        // Check if page loads without errors
        await expect(page.locator('body')).toBeVisible();
        await expect(page.locator('#root')).toBeVisible();
        
        // Check if content is present (not just empty page)
        const hasContent = await page.locator('h1, h2, h3, form, table, .card, .container').count() > 0;
        
        if (hasContent) {
          console.log(`✅ ${route.name} page loaded with content`);
        } else {
          console.log(`⚠️ ${route.name} page loaded but appears empty`);
        }
        
        // Check page title
        const title = await page.title();
        console.log(`   Title: ${title}`);
        
      } catch (error) {
        console.log(`❌ ${route.name} page failed to load: ${error}`);
      }
    }
    
    console.log('🎉 Navigation test completed');
  });

  test('should handle 404 pages gracefully', async ({ page }) => {
    console.log('🔄 Testing 404 handling...');
    
    await page.goto('/non-existent-page');
    await page.waitForLoadState('networkidle');
    
    // Page should still load (React app should handle routing)
    await expect(page.locator('body')).toBeVisible();
    await expect(page.locator('#root')).toBeVisible();
    
    // Check if there's a 404 message or if it redirects
    const has404Content = await page.locator('text=404, text=Not Found, text=Page not found').count() > 0;
    const isRedirected = !page.url().includes('non-existent-page');
    
    if (has404Content) {
      console.log('✅ 404 page is properly displayed');
    } else if (isRedirected) {
      console.log('✅ Non-existent page redirected to:', page.url());
    } else {
      console.log('⚠️ 404 handling unclear, but page loads');
    }
    
    console.log('✅ 404 handling test completed');
  });

  test('should maintain consistent layout across pages', async ({ page }) => {
    console.log('🔄 Testing layout consistency...');
    
    const testRoutes = ['/', '/login', '/patients', '/appointments'];
    
    for (const route of testRoutes) {
      console.log(`🎨 Checking layout for: ${route}`);
      
      await page.goto(route);
      await page.waitForLoadState('networkidle');
      
      // Check for consistent elements
      const hasTitle = await page.locator('title').count() > 0;
      const hasRoot = await page.locator('#root').count() > 0;
      const hasContent = await page.locator('body').isVisible();
      
      console.log(`   Title: ${hasTitle ? '✅' : '❌'}`);
      console.log(`   Root: ${hasRoot ? '✅' : '❌'}`);
      console.log(`   Content: ${hasContent ? '✅' : '❌'}`);
      
      // Check if layout elements are present (when not on login page)
      if (!route.includes('login')) {
        const hasNavigation = await page.locator('nav, .navbar, .sidebar').count() > 0;
        const hasHeader = await page.locator('header, .header').count() > 0;
        
        console.log(`   Navigation: ${hasNavigation ? '✅' : '⚠️'}`);
        console.log(`   Header: ${hasHeader ? '✅' : '⚠️'}`);
      }
    }
    
    console.log('✅ Layout consistency test completed');
  });

  test('should handle browser back/forward navigation', async ({ page }) => {
    console.log('🔄 Testing browser navigation...');
    
    // Navigate through several pages
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    console.log('📍 Started at home page');
    
    await page.goto('/login');
    await page.waitForLoadState('networkidle');
    console.log('📍 Navigated to login page');
    
    await page.goto('/patients');
    await page.waitForLoadState('networkidle');
    console.log('📍 Navigated to patients page');
    
    // Test back navigation
    await page.goBack();
    await page.waitForLoadState('networkidle');
    console.log('🔙 Went back to:', page.url());
    
    await page.goBack();
    await page.waitForLoadState('networkidle');
    console.log('🔙 Went back to:', page.url());
    
    // Test forward navigation
    await page.goForward();
    await page.waitForLoadState('networkidle');
    console.log('🔜 Went forward to:', page.url());
    
    // Verify page is still functional
    await expect(page.locator('body')).toBeVisible();
    await expect(page.locator('#root')).toBeVisible();
    
    console.log('✅ Browser navigation test completed');
  });
});
