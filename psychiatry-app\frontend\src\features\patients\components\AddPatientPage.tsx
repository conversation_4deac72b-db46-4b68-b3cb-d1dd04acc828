import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import type { PatientFormData } from '../types';
import { usePatients } from '../hooks/usePatients';
import PatientForm from './PatientForm';
import { useToast } from '../../../components/ui/Toast';

const AddPatientPage: React.FC = () => {
  const navigate = useNavigate();
  const { createPatient, isCreating } = usePatients();
  const { success, error: showError } = useToast();
  const [error, setError] = useState<string | null>(null);

  const handleSubmit = async (formData: PatientFormData) => {
    setError(null);

    try {
      const newPatient = await createPatient({
        ...formData,
        patientId: `PAT-${Date.now()}`, // Generate patient ID
        isActive: true,
      });
      success('Patient Created', `${newPatient.firstName} ${newPatient.lastName} has been successfully added.`);
      navigate('/patients');
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to create patient';
      setError(errorMessage);
      showError('Failed to Create Patient', errorMessage);
    }
  };

  return (
    <div className="max-w-2xl mx-auto space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-gray-900">Add New Patient</h1>
        <Link
          to="/patients"
          className="text-gray-600 hover:text-gray-800"
        >
          ← Back to Patients
        </Link>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <p className="text-red-600">{error}</p>
        </div>
      )}

      <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
        <PatientForm
          onSubmit={handleSubmit}
          loading={isCreating}
          submitButtonText={isCreating ? 'Creating...' : 'Create Patient'}
        />
      </div>
    </div>
  );
};

export default AddPatientPage;
