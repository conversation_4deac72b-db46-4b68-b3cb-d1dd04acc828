import { create } from 'zustand';
import axios from 'axios';

// API Configuration
const API_BASE_URL = 'http://localhost:3002';
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

export interface LabResult {
  id: string;
  testName: string;
  category: string;
  value: string;
  unit: string;
  referenceRange: string;
  status: 'normal' | 'abnormal' | 'critical';
  dateOrdered: string;
  dateCompleted: string;
  notes?: string;
}

export interface Patient {
  id: string;
  patientId: string;
  firstName: string;
  lastName: string;
  dateOfBirth: string;
  gender: string;
  phone?: string;
  email?: string;
  isActive: boolean;
  createdAt: string;
  labResults?: LabResult[];
}

interface PatientStore {
  patients: Patient[];
  selectedPatient: Patient | null;
  loading: boolean;
  error: string | null;
  
  // Actions
  fetchPatients: () => Promise<void>;
  selectPatient: (patient: Patient) => void;
  addLabResult: (patientId: string, labResult: Omit<LabResult, 'id'>) => void;
  updateLabResult: (patientId: string, labResultId: string, updates: Partial<LabResult>) => void;
  setError: (error: string | null) => void;
}

export const usePatientStore = create<PatientStore>((set, get) => ({
  patients: [],
  selectedPatient: null,
  loading: false,
  error: null,

  fetchPatients: async () => {
    set({ loading: true, error: null });
    try {
      const response = await api.get('/api/patients');
      const patients = response.data.data || [];
      set({ patients, loading: false });
    } catch (error) {
      console.error('Failed to fetch patients:', error);
      set({ error: 'Failed to fetch patients', loading: false });
    }
  },

  selectPatient: (patient: Patient) => {
    set({ selectedPatient: patient });
  },

  addLabResult: (patientId: string, labResult: Omit<LabResult, 'id'>) => {
    const { patients, selectedPatient } = get();
    const newLabResult: LabResult = {
      ...labResult,
      id: Math.random().toString(36).substr(2, 9),
    };

    // Update patients array
    const updatedPatients = patients.map(patient => {
      if (patient.id === patientId) {
        return {
          ...patient,
          labResults: [...(patient.labResults || []), newLabResult],
        };
      }
      return patient;
    });

    // Update selected patient if it matches
    const updatedSelectedPatient = selectedPatient?.id === patientId
      ? {
          ...selectedPatient,
          labResults: [...(selectedPatient.labResults || []), newLabResult],
        }
      : selectedPatient;

    set({ 
      patients: updatedPatients, 
      selectedPatient: updatedSelectedPatient 
    });
  },

  updateLabResult: (patientId: string, labResultId: string, updates: Partial<LabResult>) => {
    const { patients, selectedPatient } = get();

    // Update patients array
    const updatedPatients = patients.map(patient => {
      if (patient.id === patientId && patient.labResults) {
        return {
          ...patient,
          labResults: patient.labResults.map(labResult =>
            labResult.id === labResultId
              ? { ...labResult, ...updates }
              : labResult
          ),
        };
      }
      return patient;
    });

    // Update selected patient if it matches
    const updatedSelectedPatient = selectedPatient?.id === patientId && selectedPatient.labResults
      ? {
          ...selectedPatient,
          labResults: selectedPatient.labResults.map(labResult =>
            labResult.id === labResultId
              ? { ...labResult, ...updates }
              : labResult
          ),
        }
      : selectedPatient;

    set({ 
      patients: updatedPatients, 
      selectedPatient: updatedSelectedPatient 
    });
  },

  setError: (error: string | null) => {
    set({ error });
  },
}));
