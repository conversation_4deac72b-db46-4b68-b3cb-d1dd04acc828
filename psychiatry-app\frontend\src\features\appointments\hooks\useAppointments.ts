import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import type { Appointment, AppointmentFormData, AppointmentFilters } from '../types';
import { appointmentsApi } from '../services/appointmentsApi';
import { queryKeys, getInvalidationKeys } from '../../../lib/queryKeys';

export const useAppointments = (filters?: AppointmentFilters) => {
  const queryClient = useQueryClient();

  // Query for fetching appointments
  const {
    data: appointments = [],
    isLoading: loading,
    error,
    refetch: fetchAppointments,
  } = useQuery({
    queryKey: queryKeys.appointments.list(filters),
    queryFn: () => appointmentsApi.getAll(filters),
    staleTime: 2 * 60 * 1000, // 2 minutes (appointments change frequently)
  });

  // Mutation for creating appointments
  const createAppointmentMutation = useMutation({
    mutationFn: (appointmentData: AppointmentFormData) => appointmentsApi.create(appointmentData),
    onSuccess: (newAppointment) => {
      // Add to the appointments list cache
      queryClient.setQueriesData(
        { queryKey: queryKeys.appointments.lists() },
        (old: Appointment[] | undefined) => {
          if (!old) return [newAppointment];
          return [newAppointment, ...old];
        }
      );

      // Invalidate related queries
      getInvalidationKeys.onAppointmentChange().forEach(key => {
        queryClient.invalidateQueries({ queryKey: key });
      });
    },
  });

  // Mutation for updating appointments
  const updateAppointmentMutation = useMutation({
    mutationFn: ({ id, updates }: { id: string; updates: Partial<AppointmentFormData> }) =>
      appointmentsApi.update(id, updates),
    onSuccess: (updatedAppointment) => {
      // Update the appointment in any lists that might contain it
      queryClient.setQueriesData(
        { queryKey: queryKeys.appointments.lists() },
        (old: Appointment[] | undefined) => {
          if (!old) return old;
          return old.map((a) => (a.id === updatedAppointment.id ? updatedAppointment : a));
        }
      );

      // Update individual appointment cache
      queryClient.setQueryData(
        queryKeys.appointments.detail(updatedAppointment.id),
        updatedAppointment
      );

      // Invalidate related queries
      getInvalidationKeys.onAppointmentChange().forEach(key => {
        queryClient.invalidateQueries({ queryKey: key });
      });
    },
  });

  // Mutation for cancelling appointments
  const cancelAppointmentMutation = useMutation({
    mutationFn: ({ id, reason }: { id: string; reason?: string }) =>
      appointmentsApi.cancel(id, reason),
    onSuccess: (cancelledAppointment) => {
      // Update the appointment in any lists that might contain it
      queryClient.setQueriesData(
        { queryKey: queryKeys.appointments.lists() },
        (old: Appointment[] | undefined) => {
          if (!old) return old;
          return old.map((a) => (a.id === cancelledAppointment.id ? cancelledAppointment : a));
        }
      );

      // Update individual appointment cache
      queryClient.setQueryData(
        queryKeys.appointments.detail(cancelledAppointment.id),
        cancelledAppointment
      );

      // Invalidate related queries
      getInvalidationKeys.onAppointmentChange().forEach(key => {
        queryClient.invalidateQueries({ queryKey: key });
      });
    },
  });

  // Mutation for deleting appointments
  const deleteAppointmentMutation = useMutation({
    mutationFn: (id: string) => appointmentsApi.delete(id),
    onSuccess: (_, deletedId) => {
      // Remove the appointment from any lists that might contain it
      queryClient.setQueriesData(
        { queryKey: queryKeys.appointments.lists() },
        (old: Appointment[] | undefined) => {
          if (!old) return old;
          return old.filter((a) => a.id !== deletedId);
        }
      );

      // Remove individual appointment cache
      queryClient.removeQueries({ queryKey: queryKeys.appointments.detail(deletedId) });

      // Invalidate related queries
      getInvalidationKeys.onAppointmentChange().forEach(key => {
        queryClient.invalidateQueries({ queryKey: key });
      });
    },
  });

  return {
    appointments,
    loading,
    error: error?.message || null,
    fetchAppointments,
    createAppointment: createAppointmentMutation.mutateAsync,
    updateAppointment: updateAppointmentMutation.mutateAsync,
    cancelAppointment: cancelAppointmentMutation.mutateAsync,
    deleteAppointment: deleteAppointmentMutation.mutateAsync,
    // Additional mutation states
    isCreating: createAppointmentMutation.isPending,
    isUpdating: updateAppointmentMutation.isPending,
    isCancelling: cancelAppointmentMutation.isPending,
    isDeleting: deleteAppointmentMutation.isPending,
  };
};

export const useAppointment = (id: string) => {

  // Query for fetching individual appointment
  const {
    data: appointment,
    isLoading: loading,
    error,
    refetch: fetchAppointment,
  } = useQuery({
    queryKey: queryKeys.appointments.detail(id),
    queryFn: () => appointmentsApi.getById(id),
    enabled: !!id,
    staleTime: 2 * 60 * 1000, // 2 minutes
  });

  return {
    appointment,
    loading,
    error: error?.message || null,
    fetchAppointment,
  };
};
