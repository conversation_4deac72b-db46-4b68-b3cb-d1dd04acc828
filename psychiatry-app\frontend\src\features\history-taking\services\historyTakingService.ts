import api from '../../../lib/api';
import type { AssessmentSession } from '../types';

export const historyTakingService = {
  /**
   * Save a psychiatric assessment session
   * @param patientId The patient ID
   * @param session The assessment session data to save
   * @returns The saved assessment session with ID
   */
  async saveAssessmentSession(patientId: string, session: Partial<AssessmentSession>): Promise<AssessmentSession> {
    try {
      const response = await api.post(`/api/patients/${patientId}/assessment-sessions`, session);
      return response.data.data as AssessmentSession;
    } catch (error) {
      console.error('Error saving assessment session:', error);
      throw error;
    }
  },

  /**
   * Get assessment sessions for a patient
   * @param patientId The patient ID
   * @returns Array of assessment sessions
   */
  async getPatientAssessmentSessions(patientId: string): Promise<AssessmentSession[]> {
    try {
      const response = await api.get(`/api/patients/${patientId}/assessment-sessions`);
      return response.data.data as AssessmentSession[];
    } catch (error) {
      console.error('Error fetching assessment sessions:', error);
      throw error;
    }
  },

  /**
   * Get a specific assessment session by ID
   * @param sessionId The assessment session ID
   * @returns The assessment session
   */
  async getAssessmentSessionById(sessionId: string): Promise<AssessmentSession> {
    try {
      const response = await api.get(`/api/assessment-sessions/${sessionId}`);
      return response.data.data as AssessmentSession;
    } catch (error) {
      console.error('Error fetching assessment session:', error);
      throw error;
    }
  }
};

export default historyTakingService;