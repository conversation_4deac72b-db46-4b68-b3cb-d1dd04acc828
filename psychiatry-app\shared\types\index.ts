// Shared types between frontend and backend

export interface User {
  id: string;
  username: string;
  email: string;
  role: 'ADMIN' | 'CLINICIAN' | 'STAFF';
  firstName: string;
  lastName: string;
  isActive: boolean;
  lastLogin?: string;
  createdAt: string;
  updatedAt: string;
}

export interface Patient {
  id: string;
  patientId: string;
  firstName: string;
  lastName: string;
  dateOfBirth: string;
  gender: 'MALE' | 'FEMALE' | 'NON_BINARY' | 'PREFER_NOT_TO_SAY' | 'OTHER';
  phone?: string;
  email?: string;
  address?: PatientAddress;
  occupation?: string;
  education?: EducationLevel;
  maritalStatus?: MaritalStatus;
  emergencyContact?: EmergencyContact;
  insuranceInfo?: InsuranceInfo;
  medicalHistory?: string;
  allergies?: string;
  currentMeds?: string;
  notes?: string;
  isActive: boolean;
  isDeleted: boolean;
  deletedAt?: string;
  createdAt: string;
  updatedAt: string;
  createdBy: string;
}

export interface PatientAddress {
  street: string;
  city: string;
  state: string;
  zipCode: string;
  country?: string;
}

export interface EmergencyContact {
  name: string;
  phone: string;
  relationship: string;
  email?: string;
}

export interface InsuranceInfo {
  provider: string;
  policyNumber: string;
  groupNumber?: string;
  subscriberName?: string;
  effectiveDate?: string;
  expirationDate?: string;
}

export interface LabResult {
  id: string;
  patientId: string;
  testType: TestType;
  testDate: string;
  orderedBy: string;
  labName?: string;
  results: LabValues;
  normalRanges?: NormalRanges;
  flags?: LabFlags;
  notes?: string;
  status: ResultStatus;
  isDeleted: boolean;
  deletedAt?: string;
  createdAt: string;
  updatedAt: string;
}

export interface Appointment {
  id: string;
  patientId: string;
  date: string;
  duration: number;
  type: AppointmentType;
  status: AppointmentStatus;
  notes?: string;
  isDeleted: boolean;
  deletedAt?: string;
  createdAt: string;
  updatedAt: string;
}

// Lab-related types
export interface LabValues {
  [key: string]: number | string | boolean;
}

export interface NormalRanges {
  [key: string]: {
    min?: number;
    max?: number;
    unit?: string;
    reference?: string;
  };
}

export interface LabFlags {
  [key: string]: {
    flag: 'HIGH' | 'LOW' | 'CRITICAL' | 'ABNORMAL';
    severity: 'MILD' | 'MODERATE' | 'SEVERE';
    note?: string;
  };
}

// Enums
export type Role = 'ADMIN' | 'CLINICIAN' | 'STAFF';

export type Gender = 'MALE' | 'FEMALE' | 'NON_BINARY' | 'PREFER_NOT_TO_SAY' | 'OTHER';

export type EducationLevel = 
  | 'ELEMENTARY'
  | 'HIGH_SCHOOL'
  | 'SOME_COLLEGE'
  | 'BACHELORS'
  | 'MASTERS'
  | 'DOCTORATE'
  | 'PROFESSIONAL'
  | 'OTHER';

export type MaritalStatus = 
  | 'SINGLE'
  | 'MARRIED'
  | 'DIVORCED'
  | 'WIDOWED'
  | 'SEPARATED'
  | 'DOMESTIC_PARTNERSHIP'
  | 'OTHER';

export type TestType = 
  | 'CBC'
  | 'METABOLIC_PANEL'
  | 'LIPID_PANEL'
  | 'THYROID'
  | 'LIVER_FUNCTION'
  | 'KIDNEY_FUNCTION'
  | 'VITAMIN_LEVELS'
  | 'DRUG_SCREEN'
  | 'CARDIAC_MARKERS'
  | 'INFLAMMATORY'
  | 'COAGULATION'
  | 'URINALYSIS'
  | 'HEMOGLOBIN_A1C'
  | 'OTHER';

export type ResultStatus = 
  | 'PENDING'
  | 'IN_PROGRESS'
  | 'COMPLETED'
  | 'CANCELLED'
  | 'AMENDED';

export type AppointmentType = 
  | 'INITIAL_CONSULTATION'
  | 'FOLLOW_UP'
  | 'THERAPY_SESSION'
  | 'MEDICATION_REVIEW'
  | 'CRISIS_INTERVENTION'
  | 'GROUP_THERAPY'
  | 'FAMILY_THERAPY'
  | 'PSYCHOLOGICAL_TESTING'
  | 'OTHER';

export type AppointmentStatus = 
  | 'SCHEDULED'
  | 'CONFIRMED'
  | 'IN_PROGRESS'
  | 'COMPLETED'
  | 'CANCELLED'
  | 'NO_SHOW'
  | 'RESCHEDULED';

// API Response types
export interface ApiResponse<T = unknown> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
  errors?: Record<string, string[]>;
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

// Authentication types
export interface LoginCredentials {
  username: string;
  password: string;
}

export interface AuthTokens {
  accessToken: string;
  refreshToken: string;
}

export interface AuthResponse extends ApiResponse<AuthTokens> {
  user?: User;
}
