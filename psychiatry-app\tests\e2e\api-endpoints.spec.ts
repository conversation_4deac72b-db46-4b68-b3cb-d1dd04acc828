import { test, expect } from '@playwright/test';

test.describe('API Endpoints', () => {
  test('should have working backend health check', async ({ request }) => {
    console.log('🔄 Testing backend health endpoint...');
    
    const response = await request.get('http://localhost:3002/health');
    
    expect(response.status()).toBe(200);
    
    const data = await response.json();
    console.log('✅ Health check response:', data);
    
    expect(data.status).toBe('healthy');
    expect(data.message).toContain('Psychiatry App Server');
  });

  test('should have working stats endpoint', async ({ request }) => {
    console.log('🔄 Testing stats endpoint...');
    
    const response = await request.get('http://localhost:3002/api/stats');
    
    expect(response.status()).toBe(200);
    
    const data = await response.json();
    console.log('✅ Stats response:', data);
    
    expect(data.success).toBe(true);
    expect(data.data).toBeDefined();
    expect(typeof data.data.users).toBe('number');
    expect(typeof data.data.patients).toBe('number');
  });

  test('should have working users endpoint', async ({ request }) => {
    console.log('🔄 Testing users endpoint...');
    
    const response = await request.get('http://localhost:3002/api/users');
    
    expect(response.status()).toBe(200);
    
    const data = await response.json();
    console.log('✅ Users endpoint working, found', data.data?.length || 0, 'users');
    
    expect(data.success).toBe(true);
    expect(Array.isArray(data.data)).toBe(true);
  });

  test('should have working patients endpoint', async ({ request }) => {
    console.log('🔄 Testing patients endpoint...');
    
    const response = await request.get('http://localhost:3002/api/patients');
    
    expect(response.status()).toBe(200);
    
    const data = await response.json();
    console.log('✅ Patients endpoint working, found', data.data?.length || 0, 'patients');
    
    expect(data.success).toBe(true);
    expect(Array.isArray(data.data)).toBe(true);
  });

  test('should have working appointments endpoint', async ({ request }) => {
    console.log('🔄 Testing appointments endpoint...');
    
    const response = await request.get('http://localhost:3002/api/appointments');
    
    expect(response.status()).toBe(200);
    
    const data = await response.json();
    console.log('✅ Appointments endpoint working, found', data.data?.length || 0, 'appointments');
    
    expect(data.success).toBe(true);
    expect(Array.isArray(data.data)).toBe(true);
  });

  test('should have working lab results endpoint', async ({ request }) => {
    console.log('🔄 Testing lab results endpoint...');
    
    const response = await request.get('http://localhost:3002/api/lab-results');
    
    expect(response.status()).toBe(200);
    
    const data = await response.json();
    console.log('✅ Lab results endpoint working, found', data.data?.length || 0, 'lab results');
    
    expect(data.success).toBe(true);
    expect(Array.isArray(data.data)).toBe(true);
  });

  test('should have working authentication endpoints', async ({ request }) => {
    console.log('🔄 Testing authentication endpoints...');
    
    // Test login endpoint with invalid credentials
    const loginResponse = await request.post('http://localhost:3002/api/auth/login', {
      data: {
        email: '<EMAIL>',
        password: 'wrongpassword'
      }
    });
    
    // Should return 401 for invalid credentials
    expect(loginResponse.status()).toBe(401);
    console.log('✅ Login endpoint properly rejects invalid credentials');
    
    // Test with valid credentials
    const validLoginResponse = await request.post('http://localhost:3002/api/auth/login', {
      data: {
        email: '<EMAIL>',
        password: 'admin123'
      }
    });
    
    if (validLoginResponse.status() === 200) {
      const loginData = await validLoginResponse.json();
      console.log('✅ Login endpoint accepts valid credentials');
      expect(loginData.success).toBe(true);
      expect(loginData.data.accessToken).toBeDefined();
    } else {
      console.log('⚠️ Valid login failed - may need user setup');
    }
  });

  test('should handle CORS properly', async ({ request }) => {
    console.log('🔄 Testing CORS headers...');
    
    const response = await request.get('http://localhost:3002/health');
    
    const corsHeader = response.headers()['access-control-allow-origin'];
    console.log('CORS header:', corsHeader);
    
    // Should allow frontend origin or be permissive for development
    expect(corsHeader).toBeDefined();
    console.log('✅ CORS headers are present');
  });
});
