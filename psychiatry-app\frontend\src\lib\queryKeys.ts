// Centralized query keys for React Query
export const queryKeys = {
  // Authentication
  auth: {
    user: ['auth', 'user'] as const,
    me: ['auth', 'me'] as const,
  },

  // Patients
  patients: {
    all: ['patients'] as const,
    lists: () => [...queryKeys.patients.all, 'list'] as const,
    list: (filters?: any) => [...queryKeys.patients.lists(), { filters }] as const,
    details: () => [...queryKeys.patients.all, 'detail'] as const,
    detail: (id: string) => [...queryKeys.patients.details(), id] as const,
    stats: () => [...queryKeys.patients.all, 'stats'] as const,
  },

  // Appointments
  appointments: {
    all: ['appointments'] as const,
    lists: () => [...queryKeys.appointments.all, 'list'] as const,
    list: (filters?: any) => [...queryKeys.appointments.lists(), { filters }] as const,
    details: () => [...queryKeys.appointments.all, 'detail'] as const,
    detail: (id: string) => [...queryKeys.appointments.details(), id] as const,
    patient: (patientId: string) => [...queryKeys.appointments.all, 'patient', patientId] as const,
    upcoming: () => [...queryKeys.appointments.all, 'upcoming'] as const,
    today: () => [...queryKeys.appointments.all, 'today'] as const,
  },

  // Lab Results
  labResults: {
    all: ['labResults'] as const,
    lists: () => [...queryKeys.labResults.all, 'list'] as const,
    list: (filters?: any) => [...queryKeys.labResults.lists(), { filters }] as const,
    details: () => [...queryKeys.labResults.all, 'detail'] as const,
    detail: (id: string) => [...queryKeys.labResults.details(), id] as const,
    byPatient: (patientId: string) => [...queryKeys.labResults.all, 'patient', patientId] as const,
    templates: () => [...queryKeys.labResults.all, 'templates'] as const,
  },

  // Users
  users: {
    all: ['users'] as const,
    lists: () => [...queryKeys.users.all, 'list'] as const,
    list: (filters?: any) => [...queryKeys.users.lists(), { filters }] as const,
    details: () => [...queryKeys.users.all, 'detail'] as const,
    detail: (id: string) => [...queryKeys.users.details(), id] as const,
    profile: (id: string) => [...queryKeys.users.all, 'profile', id] as const,
  },

  // Dashboard
  dashboard: {
    all: ['dashboard'] as const,
    stats: () => [...queryKeys.dashboard.all, 'stats'] as const,
    recentActivity: () => [...queryKeys.dashboard.all, 'recentActivity'] as const,
    upcomingAppointments: () => [...queryKeys.dashboard.all, 'upcomingAppointments'] as const,
  },

  // Analytics
  analytics: {
    all: ['analytics'] as const,
    dashboard: () => [...queryKeys.analytics.all, 'dashboard'] as const,
    patientStats: (params?: any) => [...queryKeys.analytics.all, 'patientStats', params] as const,
    appointmentStats: (params?: any) => [...queryKeys.analytics.all, 'appointmentStats', params] as const,
    labResultStats: (params?: any) => [...queryKeys.analytics.all, 'labResultStats', params] as const,
    reports: () => [...queryKeys.analytics.all, 'reports'] as const,
    report: (type: string, params?: any) => [...queryKeys.analytics.reports(), type, params] as const,
  },
} as const;

// Helper function to invalidate related queries
export const getInvalidationKeys = {
  // When a patient is created/updated/deleted
  onPatientChange: () => [
    queryKeys.patients.all,
    queryKeys.dashboard.stats(),
    queryKeys.analytics.patientStats(),
  ],

  // When an appointment is created/updated/deleted
  onAppointmentChange: () => [
    queryKeys.appointments.all,
    queryKeys.dashboard.stats(),
    queryKeys.dashboard.upcomingAppointments(),
    queryKeys.analytics.appointmentStats(),
  ],

  // When a lab result is created/updated/deleted
  onLabResultChange: () => [
    queryKeys.labResults.all,
    queryKeys.dashboard.stats(),
    queryKeys.analytics.labResultStats(),
  ],

  // When a user is created/updated/deleted
  onUserChange: () => [
    queryKeys.users.all,
    queryKeys.dashboard.stats(),
  ],
};
