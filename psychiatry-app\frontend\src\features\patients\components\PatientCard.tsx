import React from 'react';
import type { Patient } from '../types';

interface PatientCardProps {
  patient: Patient;
  onViewDetails?: (patient: Patient) => void;
}

const PatientCard: React.FC<PatientCardProps> = ({ patient, onViewDetails }) => {
  const handleViewDetails = () => {
    if (onViewDetails) {
      onViewDetails(patient);
    } else {
      // Default behavior - could navigate to patient details page
      console.log('View details for patient:', patient.id);
    }
  };

  return (
    <div className="px-6 py-4 hover:bg-gray-50">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <div className="flex-shrink-0">
            <div className="h-10 w-10 bg-blue-100 rounded-full flex items-center justify-center">
              <span className="text-blue-600 font-medium">
                {patient.firstName[0]}{patient.lastName[0]}
              </span>
            </div>
          </div>
          <div>
            <h3 className="text-sm font-medium text-gray-900">
              {patient.firstName} {patient.lastName}
            </h3>
            <p className="text-sm text-gray-500">ID: {patient.patientId}</p>
            <p className="text-sm text-gray-500">
              Born: {new Date(patient.dateOfBirth).toLocaleDateString()}
            </p>
            {patient.phone && (
              <p className="text-sm text-gray-500">Phone: {patient.phone}</p>
            )}
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <span className={`px-2 py-1 text-xs rounded-full ${
            patient.isActive
              ? 'bg-green-100 text-green-800'
              : 'bg-red-100 text-red-800'
          }`}>
            {patient.isActive ? 'Active' : 'Inactive'}
          </span>
          <button 
            onClick={handleViewDetails}
            className="text-blue-600 hover:text-blue-800 text-sm font-medium"
          >
            View Details
          </button>
        </div>
      </div>
    </div>
  );
};

export default PatientCard;
