export interface DashboardStats {
  patients: {
    total: number;
    active: number;
    newThisMonth: number;
  };
  appointments: {
    total: number;
    today: number;
    thisWeek: number;
    upcoming: number;
  };
  labResults: {
    total: number;
    pending: number;
    abnormal: number;
  };
  users: {
    total: number;
    active: number;
  };
}

export interface RecentActivityItem {
  id: string;
  type: 'patient_created' | 'appointment_scheduled' | 'lab_result_added' | 'user_created';
  description: string;
  timestamp: string;
  userId?: string;
  patientId?: string;
}

export interface ChartData {
  name: string;
  value: number;
  color?: string;
}
