# 🏥 Psychiatry Patient Management System

A secure, HIPAA-compliant patient management system designed specifically for psychiatry practices. Built with modern web technologies and enterprise-grade security features.

## 🚀 Features

- **Secure Authentication**: JWT-based authentication with refresh tokens
- **Patient Management**: Comprehensive patient records with advanced search
- **Lab Data Tracking**: Complete laboratory result management with visualization
- **Data Export**: Multiple export formats including ML-ready datasets
- **Responsive Design**: Mobile-first, accessible user interface
- **HIPAA Compliance**: Built with healthcare data security in mind

## 🛠️ Tech Stack

### Frontend
- **React 18** with TypeScript
- **Vite** for fast development and building
- **Tailwind CSS** for styling
- **React Hook Form** with Zod validation
- **Recharts** for data visualization

### Backend
- **Node.js** with Express and TypeScript
- **Prisma ORM** for database management
- **SQLite** (development) / **PostgreSQL** (production)
- **JWT** authentication with bcrypt password hashing
- **Rate limiting** and security middleware

## 📋 Project Structure

```
psychiatry-app/
├── frontend/           # React frontend application
├── backend/            # Node.js backend API
├── database/           # Database files and migrations
├── shared/             # Shared TypeScript types
└── docs/               # Documentation
```

## 🚀 Quick Start

### Prerequisites
- Node.js 18+ and npm
- Git

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd psychiatry-patient-management
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   ```bash
   # Backend environment
   cp backend/.env.example backend/.env
   # Edit backend/.env with your configuration
   
   # Frontend environment
   cp frontend/.env.example frontend/.env
   # Edit frontend/.env with your configuration
   ```

4. **Initialize the database**
   ```bash
   npm run db:generate
   npm run db:push
   npm run db:seed
   ```

5. **Start development servers**
   ```bash
   npm run dev
   ```

   This will start:
   - Frontend: http://localhost:5173
   - Backend API: http://localhost:3001
   - Database Studio: http://localhost:5555 (run `npm run db:studio`)

## 📚 Available Scripts

- `npm run dev` - Start both frontend and backend in development mode
- `npm run build` - Build both applications for production
- `npm run test` - Run all tests
- `npm run lint` - Lint all code
- `npm run format` - Format code with Prettier
- `npm run db:studio` - Open Prisma Studio for database management

## 🔒 Security Features

- **Authentication**: JWT tokens with automatic refresh
- **Password Security**: bcrypt hashing with salt rounds
- **Input Validation**: Comprehensive validation on all endpoints
- **Rate Limiting**: Protection against brute force attacks
- **CORS Configuration**: Secure cross-origin resource sharing
- **Helmet.js**: Security headers and protection middleware

## 🏗️ Development Phases

This project is built in phases:

- ✅ **Phase 0**: Foundation & Environment Setup
- 🔄 **Phase 1**: Security Foundation & Authentication
- ⏳ **Phase 2**: Patient Management Core
- ⏳ **Phase 3**: Advanced UI Components & Design System
- ⏳ **Phase 4**: Laboratory Data Management
- ⏳ **Phase 5**: Advanced Analytics & Export System
- ⏳ **Phase 6**: Enterprise Features & Optimization

## 📖 API Documentation

API documentation is available at `/api/docs` when running the development server.

## 🧪 Testing

```bash
# Run all tests
npm run test

# Run frontend tests
npm run test:frontend

# Run backend tests
npm run test:backend
```

## 🚀 Deployment

Detailed deployment instructions for various platforms are available in the `docs/deployment/` directory.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions, please open an issue in the GitHub repository.

---

**Built with ❤️ for healthcare professionals**
