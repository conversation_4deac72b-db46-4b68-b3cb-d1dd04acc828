import React from 'react';
import { Link } from 'react-router-dom';
import { Plus, Users } from 'lucide-react';

const UsersPage: React.FC = () => {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold text-gray-900">Staff Members</h1>
        <Link
          to="/users/new"
          className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center"
        >
          <Plus className="h-4 w-4 mr-2" />
          Add Staff Member
        </Link>
      </div>

      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8 text-center">
        <Users className="h-16 w-16 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">Staff Management Module</h3>
        <p className="text-gray-600 mb-4">
          This feature is being migrated to the new architecture.
        </p>
        <p className="text-sm text-gray-500">
          Coming soon with enhanced user management capabilities.
        </p>
      </div>
    </div>
  );
};

export default UsersPage;
