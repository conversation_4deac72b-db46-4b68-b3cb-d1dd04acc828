@echo off
echo ========================================
echo   Psychiatry Patient Management System
echo ========================================
echo.
echo Starting the application...
echo.

REM Change to the project directory
cd /d "C:\Users\<USER>\projects\4"

REM Check if we're in the right directory
if not exist "package.json" (
    echo ERROR: Not in the correct project directory
    echo Current directory: %CD%
    pause
    exit /b 1
)

REM Clean install dependencies if needed
if not exist "backend\node_modules" (
    echo Installing backend dependencies...
    cd backend
    call npm install
    if errorlevel 1 (
        echo ERROR: Failed to install backend dependencies
        pause
        exit /b 1
    )
    cd ..
)

if not exist "frontend\node_modules" (
    echo Installing frontend dependencies...
    cd frontend
    call npm install
    if errorlevel 1 (
        echo ERROR: Failed to install frontend dependencies
        pause
        exit /b 1
    )
    cd ..
)

REM Install root dependencies for concurrently (only if package.json exists in root)
if exist "package.json" (
    if not exist "node_modules" (
        echo Installing root dependencies...
        call npm install
        if errorlevel 1 (
            echo ERROR: Failed to install root dependencies
            pause
            exit /b 1
        )
    )
)

REM Generate Prisma client if needed
echo Generating database client...
cd backend
call npx prisma generate
if errorlevel 1 (
    echo ERROR: Failed to generate Prisma client
    cd ..
    pause
    exit /b 1
)

REM Push database schema
echo Setting up database...
call npx prisma db push
if errorlevel 1 (
    echo ERROR: Failed to setup database
    cd ..
    pause
    exit /b 1
)

REM Seed database with initial data (optional)
if exist "prisma\seed.js" (
    echo Seeding database with initial data...
    call npx prisma db seed
    if errorlevel 1 (
        echo WARNING: Database seeding failed, but continuing...
    )
) else (
    echo No seed file found, skipping database seeding...
)

cd ..

REM Get backend port from environment
set BACKEND_PORT=3001
if exist "backend\.env" (
    for /f "tokens=2 delims==" %%a in ('findstr "^PORT=" backend\.env') do set BACKEND_PORT=%%a
)

REM Start the application
echo.
echo ========================================
echo   Starting Application Servers
echo ========================================
echo.
echo Backend will start on: http://localhost:%BACKEND_PORT%
echo Frontend will start on: http://localhost:5173
echo.

REM Check if we have a root package.json with dev script
if exist "package.json" (
    findstr /c:"\"dev\"" package.json >nul
    if not errorlevel 1 (
        echo Using root npm dev script...
        call npm run dev
    ) else (
        echo No dev script in root package.json, starting servers manually...
        goto :manual_start
    )
) else (
    echo No root package.json found, starting servers manually...
    goto :manual_start
)

goto :end

:manual_start
echo Starting servers in separate windows...
start "Psychiatry Backend" cmd /k "cd /d %CD%\backend && npm start"
timeout /t 3 /nobreak >nul
start "Psychiatry Frontend" cmd /k "cd /d %CD%\frontend && npm run dev"
echo.
echo Both servers are starting in separate windows...
echo - Backend: Check the "Psychiatry Backend" window
echo - Frontend: Check the "Psychiatry Frontend" window
echo.
echo You can close this window now.

:end
pause